<script lang="ts">
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import { onMount } from 'svelte';
  import type { PageData } from './$types';
  import TaskForm from '$lib/components/TaskForm.svelte';
  import PageTransition from '$lib/components/ui/PageTransition.svelte';
  import { toasts } from '$lib/stores/toast';

  export let data: PageData;

  let title = '';
  let notes = '';
  let priority = 1;
  let categoryId = '';
  let dueDate = '';
  let dueTime = '';
  let hasDueDate = false;
  let subtasks: string[] = [''];
  let loading = false;
  let error = '';
  let success = '';
  let showCategoryDropdown = false;

  // New recurrence rule state
  let hasRecurrence = false;
  let recurrenceRule: any = null;
  let reminderDays: number | null = 3;
  let emailReminder = true;

  // Handle URL date parameter
  onMount(() => {
    const urlDate = $page.url.searchParams.get('date');
    if (urlDate) {
      dueDate = urlDate;
      hasDueDate = true;
    }
  });

  async function handleSubmit(event: CustomEvent) {
    const formData = event.detail;
    
    loading = true;
    error = '';

    try {
      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok) {
        // Show success toast and redirect
        toasts.success('Task created!', `"${formData.title}" has been added to your tasks.`);
        goto('/dashboard/tasks', { invalidateAll: true });
      } else {
        error = result.error || 'Failed to create task';
        toasts.error('Failed to create task', error);
      }
    } catch (err) {
      error = 'Network error. Please try again.';
      toasts.error('Network error', 'Unable to create task. Please check your connection.');
    } finally {
      loading = false;
    }
  }


</script>

<svelte:head>
  <title>New Task - Routine Mail</title>
</svelte:head>

<PageTransition>
<div class="page-container">
  <!-- Desktop Header -->
  <div class="page-header hidden md:block">
    <h1 class="page-title">Create New Task</h1>
    <p class="page-subtitle">Add a new task to your routine</p>
  </div>

  <TaskForm
    mode="create"
    bind:title
    bind:notes
    bind:priority
    bind:categoryId
    bind:dueDate
    bind:dueTime
    bind:hasDueDate
    bind:subtasks
    bind:loading
    bind:error
    bind:success
    bind:showCategoryDropdown
    bind:hasRecurrence
    bind:recurrenceRule
    bind:reminderDays
    userTimezone={data.user?.timezone || 'Asia/Kuala_Lumpur'}
    categories={data.categories}
    on:submit={handleSubmit}
  />
</div>

<style>
  .page-container {
    max-width: 800px;
    margin: 0 auto;
  }

  .page-header {
    margin-bottom: 1rem;
  }

  .breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
  }

  .breadcrumb a {
    color: #3b82f6;
    text-decoration: none;
  }

  .breadcrumb a:hover {
    text-decoration: underline;
  }

  .breadcrumb .current {
    color: #1f2937;
    font-weight: 500;
  }

  .page-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.25rem;
    letter-spacing: -0.02em;
  }

  .page-subtitle {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
    font-weight: 500;
  }

  /* Mobile-first improvements */
  @media (max-width: 768px) {
    .page-header {
      margin-bottom: 0.75rem;
    }
  }
</style>
</PageTransition>
